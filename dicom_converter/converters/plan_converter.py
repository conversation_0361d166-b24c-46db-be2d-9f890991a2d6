"""
Plan converter for SimpleDicomConverter.

This module provides functionality to convert Pinnacle plan files to DICOM RT Plan files (RTPLAN).
It contains the PlanConverter class, which supports both archive-based and in-memory workflows.

The module handles extraction of beam parameters, prescription information, and other
treatment plan details from Pinnacle data, and formats them according to DICOM standards.

Typical Usage:
    # Plan Converter From Models
    plan_converter = PlanConverter(patient)
    plan_ds = plan_converter.convert(trial)
    plan_converter.save_dataset(plan_ds, output_path)

    # Plan Converter From Archive
    plan_converter = PlanConverter.from_archive(patient_path)
    trial_list = plan_converter.load_trials(plan_path)
    plan_ds = plan_converter.convert(trial_list[trial_index])
    plan_converter.save_dataset(plan_ds, output_path)
"""

import logging
import time
import sys
import argparse
import pydicom
from pydicom.dataset import Dataset, FileDataset
from pydicom.sequence import Sequence
from pydicom.uid import generate_uid

from dicom_converter.converters.base_converter import TrialBaseConverter
from pinnacle_io.models import Trial, Patient
from dicom_converter.utils.constants import (
    RT_PLAN_SOP_CLASS_UID,
    MODALITY_RTPLAN,
    DEFAULT_DOSE_RATE,
    BEAM_LIMITING_DEVICE_TYPE_ASYMX,
    BEAM_LIMITING_DEVICE_TYPE_ASYMY,
    BEAM_LIMITING_DEVICE_TYPE_MLCX,
    ROTATION_DIRECTION_NONE,
    MLC_LEAF_JAW_PAIRS,
    MLC_LEAF_POSITION_BOUNDARIES,
    APPROVAL_STATUS_UNAPPROVED,
)

logger = logging.getLogger(__name__)


class PlanConverter(TrialBaseConverter):
    """
    Converter for Pinnacle RT Plan (RTPLAN) DICOM objects.

    This class provides methods to read Pinnacle plan files, extract beam and prescription information,
    and generate a DICOM-compliant RT Plan dataset. It supports both archive-based and in-memory workflows,
    and can be used as a command-line tool or as a Python class.

    The converter handles various plan components including:
    - Plan identification and metadata
    - Beam parameters (gantry angles, collimator settings, etc.)
    - MLC configurations
    - Dose prescriptions
    - Fraction groups
    - Patient setup information
    """

    MODALITY = MODALITY_RTPLAN

    def __init__(self, patient: Patient):
        """
        Initialize the plan converter with a Patient model.

        Args:
            patient: Patient model object.
        """
        # Initialize the base converter with the patient model
        super().__init__(patient)

    @classmethod
    def from_archive(cls, patient_path: str) -> "PlanConverter":
        """
        Create a PlanConverter instance from a Pinnacle archive.

        This method loads the patient data from files in the archive
        and returns a PlanConverter instance that can be used to convert the data
        to DICOM RT Plan format.

        Args:
            patient_path: Full path to the patient folder in the Pinnacle archive.
                          This should contain the patient's demographic and plan data.

        Returns:
            PlanConverter instance initialized with patient data from the archive.
        """
        return super().from_archive(patient_path)

    def convert(self, trial: Trial) -> FileDataset:
        """
        Convert Pinnacle plan files to a DICOM RT Plan dataset.

        This method ensures all required models (patient, trial) are loaded, then:
            1. Generates all required DICOM UIDs.
            2. Extracts structure information if available.
            3. Creates a base DICOM dataset for RT Plan.
            4. Populates the dataset with plan, prescription, beam, and reference information.

        Args:
            trial: Trial to convert.

        Returns:
            FileDataset: DICOM RT Plan dataset for the specified trial.

        Raises:
            Exception: If any step of the conversion fails.
        """
        try:
            # Create RT Plan dataset using helper methods to build the DICOM structure
            sop_instance_uid = generate_uid()
            file_meta = self.create_file_meta(sop_class_uid=RT_PLAN_SOP_CLASS_UID, sop_instance_uid=sop_instance_uid)
            ds = self.create_dataset(file_meta)
            self.set_common_elements(ds)
            self.set_rt_plan_elements(ds, trial)

            self.logger.info("Plan conversion completed")
            return ds
        except Exception as e:
            self.logger.error(f"Error during plan conversion: {e}")
            raise

    def set_rt_plan_elements(self, ds: Dataset, trial: Trial) -> Dataset:
        """
        Set RT Plan-specific DICOM elements in the dataset.

        This method populates the DICOM dataset with RT Plan elements according to
        DICOM standard requirements for RT Plan objects.

        Populates:
            - Plan label, name, date, time, geometry
            - Prescription and dose reference sequences
            - Patient setup, fraction group, and beam sequences
            - Approval status and referenced structure set if available

        Args:
            ds: The DICOM dataset to populate
            trial: The Trial object containing plan information

        Returns:
            Dataset: The updated DICOM dataset with RT Plan elements
        """
        ds.Modality = MODALITY_RTPLAN
        ds.RTPlanLabel = trial.name if trial and hasattr(trial, "name") else "Plan"
        ds.RTPlanName = ds.RTPlanLabel
        ds.RTPlanDate = time.strftime("%Y%m%d")
        ds.RTPlanTime = time.strftime("%H%M%S")
        ds.RTPlanGeometry = "PATIENT"
        ds.PrescriptionDescription = getattr(trial.prescription_list[0], "description", "") if trial and hasattr(trial, "prescription_list") else ""
        ds.DoseReferenceSequence = self.create_dose_reference_sequence(trial)
        ds.PatientSetupSequence = self.create_patient_setup_sequence(trial)
        ds.FractionGroupSequence = self.create_fraction_group_sequence(trial)
        ds.BeamSequence = self.create_beam_sequence(trial)
        ds.ApprovalStatus = APPROVAL_STATUS_UNAPPROVED
        # Reference to structure set - uncomment when structure set reference is available
        # if self.structure_sop_instance_uid:
        #     ds.ReferencedStructureSetSequence = Sequence()
        #     referenced_structure = Dataset()
        #     referenced_structure.ReferencedSOPClassUID = "1.2.840.10008.*******.1.481.3"  # RT Structure Set Storage
        #     referenced_structure.ReferencedSOPInstanceUID = self.structure_sop_instance_uid
        #     ds.ReferencedStructureSetSequence.append(referenced_structure)
        return ds

    def create_dose_reference_sequence(self, trial: Trial) -> Sequence:
        """
        Create the Dose Reference Sequence for the RT Plan.

        This sequence contains information about the prescribed dose to target volumes.

        Args:
            trial: The Trial object containing prescription information

        Returns:
            Sequence: DICOM Sequence containing dose reference information
        """
        dose_ref_seq = Sequence()
        if trial and hasattr(trial, "prescription_list") and trial.prescription_list:
            dose_ref = Dataset()
            dose_ref.DoseReferenceNumber = "1"
            dose_ref.DoseReferenceStructureType = "SITE"
            dose_ref.DoseReferenceDescription = "Plan Target Volume"
            dose_ref.TargetPrescriptionDose = getattr(trial.prescription_list[0], "dose", 0.0)
            dose_ref.DoseReferenceType = "TARGET"
            dose_ref_seq.append(dose_ref)
        return dose_ref_seq

    def create_patient_setup_sequence(self, trial: Trial) -> Sequence:
        """
        Create the Patient Setup Sequence for the RT Plan.

        This sequence describes how the patient is positioned for treatment delivery.
        One setup is created for each beam in the plan.

        Args:
            trial: The Trial object containing beam information

        Returns:
            Sequence: DICOM Sequence containing patient setup information
        """
        setup_seq = Sequence()
        for i in range(max(1, len(getattr(trial, "beams", [])))):
            setup = Dataset()
            setup.PatientSetupNumber = i + 1
            setup.PatientPosition = getattr(self, "patient_position", "HFS") or "HFS"
            setup_seq.append(setup)
        return setup_seq

    def create_fraction_group_sequence(self, trial: Trial) -> Sequence:
        """
        Create the Fraction Group Sequence for the RT Plan.

        This sequence describes the fractionation scheme and references the beams
        that are to be delivered in each fraction.

        Args:
            trial: The Trial object containing prescription and beam information

        Returns:
            Sequence: DICOM Sequence containing fraction group information
        """
        fraction_seq = Sequence()
        fraction_group = Dataset()
        fraction_group.FractionGroupNumber = 1
        fraction_group.NumberOfFractionsPlanned = (
            getattr(trial.prescription_list[0], "number_of_fractions", 1) if trial and hasattr(trial, "prescription_list") else 1
        )
        fraction_group.NumberOfBeams = len(getattr(trial, "beams", []))
        fraction_group.NumberOfBrachyApplicationSetups = 0
        fraction_group.ReferencedBeamSequence = Sequence()
        for i, beam in enumerate(getattr(trial, "beams", [])):
            ref_beam = Dataset()
            ref_beam.ReferencedBeamNumber = i + 1
            ref_beam.BeamMeterset = getattr(beam, "monitor_units", 0)
            if hasattr(beam, "monitor_units") and beam.monitor_units:
                ref_beam.BeamDose = beam.monitor_units / 100  # Convert MU to Gy (assuming 1 Gy = 100 MU calibration)
            fraction_group.ReferencedBeamSequence.append(ref_beam)
        fraction_seq.append(fraction_group)
        return fraction_seq

    def create_beam_sequence(self, trial: Trial) -> Sequence:
        """
        Create the Beam Sequence for the RT Plan.

        This sequence contains detailed information about each treatment beam,
        including beam type, energy, machine name, and delivery parameters.

        Args:
            trial: The Trial object containing beam information

        Returns:
            Sequence: DICOM Sequence containing beam information for all beams in the plan
        """
        beam_seq = Sequence()
        for i, beam_model in enumerate(getattr(trial, "beams", [])):
            beam = Dataset()
            beam.BeamNumber = i + 1
            beam.BeamName = getattr(beam_model, "name", f"Beam_{i+1}")
            beam.BeamType = getattr(beam_model, "type", "STATIC")
            beam.RadiationType = getattr(beam_model, "radiation_type", "PHOTON")
            beam.NominalBeamEnergy = getattr(beam_model, "energy", 6)
            beam.TreatmentMachineName = getattr(beam_model, "machine_name", "LINAC")
            beam.Manufacturer = "Pinnacle Philips"
            beam.SourceAxisDistance = getattr(beam_model, "sad", 1000)
            beam.BeamLimitingDeviceSequence = self.create_beam_limiting_device_sequence(beam_model)
            beam.TreatmentDeliveryType = "TREATMENT"
            beam.ReferencedPatientSetupNumber = i + 1
            beam.PrimaryFluenceModeSequence = Sequence([Dataset()])
            beam.PrimaryFluenceModeSequence[0].FluenceMode = "STANDARD"
            beam.ControlPointSequence = self.create_control_point_sequence(beam_model)
            beam.NumberOfControlPoints = len(beam.ControlPointSequence)
            beam.FinalCumulativeMetersetWeight = 1.0
            beam.PrimaryDosimeterUnit = "MU"
            beam_seq.append(beam)
        return beam_seq

    def create_beam_limiting_device_sequence(self, beam_model) -> Sequence:
        """
        Create the Beam Limiting Device Sequence for a beam.

        This sequence describes the beam limiting devices (jaws and MLC)
        available for the treatment machine.

        Args:
            beam_model: The beam model object containing collimation information

        Returns:
            Sequence: DICOM Sequence containing beam limiting device information
        """
        device_seq = Sequence()
        x_jaws = Dataset()
        x_jaws.RTBeamLimitingDeviceType = BEAM_LIMITING_DEVICE_TYPE_ASYMX
        x_jaws.NumberOfLeafJawPairs = 1
        device_seq.append(x_jaws)
        y_jaws = Dataset()
        y_jaws.RTBeamLimitingDeviceType = BEAM_LIMITING_DEVICE_TYPE_ASYMY
        y_jaws.NumberOfLeafJawPairs = 1
        device_seq.append(y_jaws)
        if hasattr(beam_model, "mlc") and beam_model.mlc:
            mlc = Dataset()
            mlc.RTBeamLimitingDeviceType = BEAM_LIMITING_DEVICE_TYPE_MLCX
            mlc.NumberOfLeafJawPairs = MLC_LEAF_JAW_PAIRS
            mlc.LeafPositionBoundaries = MLC_LEAF_POSITION_BOUNDARIES
            device_seq.append(mlc)
        return device_seq

    def create_control_point_sequence(self, beam_model) -> Sequence:
        """
        Create the Control Point Sequence for a beam.

        This sequence describes the state of the treatment machine at each control point.
        For static beams, this typically includes two control points:
        - CP0 (index 0): Contains all beam parameters at the start of delivery
        - CP1 (index 1): Contains only the cumulative meterset weight at the end of delivery

        Args:
            beam_model: The beam model object containing delivery parameters

        Returns:
            Sequence: DICOM Sequence containing control point information
        """
        control_point_seq = Sequence()
        cp0 = Dataset()
        cp0.ControlPointIndex = 0
        cp0.CumulativeMetersetWeight = 0.0
        cp0.NominalBeamEnergy = getattr(beam_model, "energy", 6)
        cp0.DoseRateSet = DEFAULT_DOSE_RATE
        cp0.GantryAngle = getattr(beam_model, "gantry_angle", 0)
        cp0.GantryRotationDirection = ROTATION_DIRECTION_NONE
        cp0.BeamLimitingDeviceAngle = getattr(beam_model, "collimator_angle", 0)
        cp0.BeamLimitingDeviceRotationDirection = ROTATION_DIRECTION_NONE
        cp0.PatientSupportAngle = getattr(beam_model, "couch_angle", 0)
        cp0.PatientSupportRotationDirection = ROTATION_DIRECTION_NONE
        cp0.IsocenterPosition = getattr(beam_model, "isocenter", [0, 0, 0])
        cp0.SourceToSurfaceDistance = getattr(beam_model, "ssd", 1000)
        cp0.BeamLimitingDevicePositionSequence = Sequence()
        x_jaws_pos = Dataset()
        x_jaws_pos.RTBeamLimitingDeviceType = BEAM_LIMITING_DEVICE_TYPE_ASYMX
        x_jaws_pos.LeafJawPositions = getattr(beam_model, "x_jaws", [-100, 100])
        cp0.BeamLimitingDevicePositionSequence.append(x_jaws_pos)
        y_jaws_pos = Dataset()
        y_jaws_pos.RTBeamLimitingDeviceType = BEAM_LIMITING_DEVICE_TYPE_ASYMY
        y_jaws_pos.LeafJawPositions = getattr(beam_model, "y_jaws", [-100, 100])
        cp0.BeamLimitingDevicePositionSequence.append(y_jaws_pos)
        if hasattr(beam_model, "mlc") and beam_model.mlc:
            mlc_pos = Dataset()
            mlc_pos.RTBeamLimitingDeviceType = BEAM_LIMITING_DEVICE_TYPE_MLCX
            mlc_pos.LeafJawPositions = getattr(beam_model.mlc, "positions", [])
            cp0.BeamLimitingDevicePositionSequence.append(mlc_pos)
        control_point_seq.append(cp0)
        cp1 = Dataset()
        cp1.ControlPointIndex = 1
        cp1.CumulativeMetersetWeight = 1.0
        control_point_seq.append(cp1)
        return control_point_seq


def main(plan_path: str, trial_index: int, output_path: str) -> None:
    """
    Main function to run the PlanConverter as a standalone script.

    Args:
        plan_path: Name of the plan folder (optional for pre-loaded workflow).
        trial_index: Trial index for conversion, starting at 0 (optional for pre-loaded workflow).
        output_path: Directory where output DICOM files will be saved.
    """
    import os

    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    logger = logging.getLogger(__name__)
    logger.info(f"Starting plan conversion for trial {trial_index} in {plan_path}")

    try:
        # Initialize the plan converter from the patient archive
        patient_path = os.path.dirname(plan_path)
        converter = PlanConverter.from_archive(patient_path)

        # Load trials from plan file and convert the selected trial
        trials = converter.load_trials(plan_path)
        trial = trials[trial_index]
        plan_dataset = converter.convert(trial)

        # Save the plan file
        output_file = converter.save_dataset(plan_dataset, output_path)

        logger.info(f"Plan conversion completed. Output file: {output_file}")

    except Exception as e:
        logger.error(f"Error during plan conversion: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":

    # Create argument parser
    parser = argparse.ArgumentParser(description="Convert Pinnacle plan files to DICOM RT Plan files")

    # Add required arguments
    parser.add_argument("--plan-path", "-f", required=True, help="Path to the plan folder")
    parser.add_argument("--trial-index", "-n", type=int, default=0, help="Trial index (default: 0)")
    parser.add_argument(
        "--output-path",
        "-o",
        required=True,
        help="Path where output DICOM files will be saved",
    )

    # Parse arguments
    args = parser.parse_args()

    # Call main function with parsed arguments
    main(
        plan_path=args.plan_path,
        trial_index=args.trial_index,
        output_path=args.output_path,
    )
