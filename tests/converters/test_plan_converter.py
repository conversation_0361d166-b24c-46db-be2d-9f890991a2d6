import os
import sys
import glob
import pytest
import subprocess
from dicom_converter.converters.plan_converter import PlanConverter
from pinnacle_io.models import Patient, Plan, Trial


def make_dummy_patient():
    patient = Patient(12345, "JOHN", "DOE")
    patient.patient_position = "HFS"
    patient.patient_path = "."
    return patient


def make_dummy_plan():
    plan = Plan(1, "Test Plan")
    plan.primary_ct_image_set_id = 0
    return plan


def make_dummy_trial():
    trial = Trial()
    trial.name = "TestPlan"
    trial.prescription_list = []
    trial.beam_list = []
    return trial


class TestPlanConverter:
    @pytest.fixture
    def plan_converter(self, tmp_path):
        # Setup test directories
        patient_folder = "Patient_1"
        patient_path = tmp_path / patient_folder
        os.makedirs(patient_path, exist_ok=True)

        # Create temporary "Patient" file
        patient_file = patient_path / "Patient"
        with open(patient_file, "w") as f:
            f.write("PatientID = 12345;\n")
            f.write('LastName = "DOE";\n')
            f.write('FirstName = "JOHN";\n')

        # Initialize converter with patient path
        converter = PlanConverter.from_archive(patient_path=str(patient_path))
        return converter

    def test_plan_converter_initialization(self, plan_converter):
        """
        Test initialization of PlanConverter class.

        Verifies that:
        - The patient object is properly initialized and not None
        - The patient last name is correctly set to "DOE"

        Args:
            plan_converter: Fixture providing initialized PlanConverter instance

        Returns:
            None
        """
        assert plan_converter.patient is not None
        assert plan_converter.patient.last_name == "DOE"

    def test_plan_converter_for_test_patient(self, tmp_path):
        """
        Integration test for PlanConverter using test patient data.

        Tests:
        - Loading patient data from test archive
        - Converting plan data to DICOM RT Plan format
        - Saving DICOM file to output directory

        Uses:
        - Input: tests/test_data/archive_01/Institution_1/Mount_0/Patient_1
        - Plan: Plan_0
        - Output: Patient_1/Dicom/Plan_0

        Any existing RT Plan DICOM files in output folder are deleted before test.

        Args:
            tmp_path: Temporary path fixture from pytest

        Raises:
            AssertionError: If test data is missing or conversion fails
        """
        test_data_path = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "..", "test_data", "archive_01", "Institution_1", "Mount_0", "Patient_1")
        )
        assert os.path.exists(test_data_path), "Test data not available"

        patient_path = test_data_path
        plan_folder = "Plan_0"
        plan_path = os.path.join(patient_path, plan_folder)
        output_path = os.path.join(patient_path, "Dicom", plan_folder)
        if os.path.exists(output_path):
            for f in glob.glob(os.path.join(output_path, "RP*.dcm")):
                os.remove(f)

        plan_converter = PlanConverter.from_archive(patient_path)
        trials = plan_converter.load_trials(plan_path)
        assert len(trials) > 0, "No trials found in test data"

        trial = trials[0]
        plan_dataset = plan_converter.convert(trial)
        output_file = plan_converter.save_dataset(plan_dataset, output_path)
        assert os.path.exists(output_file), f"Output file does not exist: {output_file}"

    def test_plan_converter_from_models(self, tmp_path):
        """Tests the conversion of plan models to DICOM RT Plan datasets.

        Tests the PlanConverter class functionality by creating dummy patient, plan and trial models,
        setting required UIDs, and verifying the converted DICOM RT Plan dataset contains all
        required attributes and sequences according to DICOM standards.

        Args:
            tmp_path (Path): Pytest fixture providing temporary directory path for test files

        Tests:
            - Creation of valid DICOM RT Plan dataset
            - Presence of required DICOM attributes and sequences:
                - Modality
                - RTPlanLabel
                - RTPlanDate/Time
                - RTPlanGeometry
                - DoseReferenceSequence
                - PatientSetupSequence
                - FractionGroupSequence
                - BeamSequence
                - ApprovalStatus
            - Correct SOP Class UID for RT Plan Storage
            - Prescription dose settings (optional)
        """
        # Create test models
        patient = make_dummy_patient()
        patient.patient_path = str(tmp_path)
        plan = make_dummy_plan()
        trial = make_dummy_trial()
        plan_path = tmp_path / plan.plan_folder
        os.makedirs(plan_path, exist_ok=True)
        converter = PlanConverter(patient)

        # Set required UIDs for DICOM dataset
        converter.sop_instance_uid = "*******.5"
        converter.series_instance_uid = "*******.5.6"
        converter.frame_of_reference_uid = "*******.5.6.7"
        plan_dataset = converter.convert(trial)

        # Expanded asserts for DICOM RT Plan dataset
        assert plan_dataset.Modality == "RTPLAN"
        assert hasattr(plan_dataset, "RTPlanLabel")
        assert plan_dataset.RTPlanLabel == trial.name
        assert hasattr(plan_dataset, "RTPlanDate")
        assert hasattr(plan_dataset, "RTPlanTime")
        assert hasattr(plan_dataset, "RTPlanGeometry")
        assert hasattr(plan_dataset, "DoseReferenceSequence")
        assert hasattr(plan_dataset, "PatientSetupSequence")
        assert hasattr(plan_dataset, "FractionGroupSequence")
        assert hasattr(plan_dataset, "BeamSequence")
        assert hasattr(plan_dataset, "ApprovalStatus")
        assert plan_dataset.file_meta.MediaStorageSOPClassUID.name == "RT Plan Storage"

        # Optional: check prescription and dose fields
        if hasattr(plan_dataset, "DoseReferenceSequence") and len(plan_dataset.DoseReferenceSequence) > 0:
            dose_ref = plan_dataset.DoseReferenceSequence[0]
            assert hasattr(dose_ref, "TargetPrescriptionDose")
            assert dose_ref.TargetPrescriptionDose == 60.0

    def test_plan_converter_command_line_interface(self):
        """Test the command line interface functionality of the plan converter.
        This test verifies that the plan converter can be successfully executed via command line,
        converting a Pinnacle plan to DICOM format. It checks:
        1. The conversion process completes without errors
        2. The output DICOM RT Plan file is created in the expected location
        Args: None
        Returns: None
        Raises:
            AssertionError: If the command execution fails or if the output DICOM file is not found
        """
        patient_path = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "..", "test_data", "archive_01", "Institution_1", "Mount_0", "Patient_1")
        )
        plan_path = os.path.join(patient_path, "Plan_0")
        trial_index = 0
        output_path = os.path.join(patient_path, "Dicom", "Plan_0", f"Trial_{trial_index}")

        # Remove existing RT Plan DICOM files
        if os.path.exists(output_path):
            for f in glob.glob(os.path.join(output_path, "RP*.dcm")):
                os.remove(f)

        # Define the command to run the CLI
        command = [
            sys.executable,
            "-m",
            "dicom_converter.converters.plan_converter",
            "--plan-path",
            plan_path,
            "--trial-index",
            str(trial_index),
            "--output-path",
            output_path,
        ]

        # Run the command and capture output
        result = subprocess.run(command, capture_output=True, text=True)

        # Check if the command was successful
        assert result.returncode == 0, f"Command failed: {result.stderr}"
        assert len(glob.glob(os.path.join(output_path, "RP*.dcm"))) == 1, "Output DICOM file not found in expected location"
